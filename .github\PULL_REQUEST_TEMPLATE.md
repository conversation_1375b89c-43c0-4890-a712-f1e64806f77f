<!--

Thanks for submitting a PR to OPA!

Before pressing 'Create pull request' please read the checklist below.

* All code changes should be accompanied with tests. If you are not
modifying any tests, just provide a short explanation of why updates
to tests are not necessary. In addition to helping catch bugs, tests
are extremely helpful in providing _context_ that explains how your
changes can be used.

* All changes to public APIs **must** be accompanied with
docs. Examples of public APIs include built-in functions,
config fields, and of course, exported Go types/functions/constants/etc.

* Commit messages should explain _why_ you made the changes, not what
you changed. Use active voice. Keep the subject line under 50
characters or so.

* All commits must be signed off by the author. If you are not
familiar with signing off, see our contributor guide below.

For more information on contributing to OPA see:

* [Contributing Guide](https://www.openpolicyagent.org/docs/contributing/)
  for high-level contributing guidelines and development setup.
  (See the [Developer Certificate of Origin](https://www.openpolicyagent.org/docs/contrib-code/#developer-certificate-of-origin)
  section for specifics on signing off a commit.)

-->

### Why the changes in this PR are needed?

<!--
Include a short description of WHY the changes were made.
-->

### What are the changes in this PR?

<!--
Include a short description of WHAT changes were made.
-->

### Notes to assist PR review:

<!--
Here you can add information you think will help the reviewer(s).
-->

### Further comments:

<!--
Here you can include links to additional resources related to the changes, discuss your solution, other approaches you considered etc.
-->
