# GRCOS Workflows Module OSCAL Integration

## Overview

The Workflows Module automates business processes using OSCAL Assessment Activities and Task Management integrated with Flowable engine. This module orchestrates compliance workflows, approval chains, and automated responses based on OSCAL document lifecycle events and assessment results.

## OSCAL Model Integration

### Assessment Activities to Workflow Translation

#### OSCAL Task Structure
```json
{
  "tasks": [
    {
      "uuid": "task-001-control-review",
      "type": "action",
      "title": "Quarterly Control Review",
      "description": "Review and validate control implementation effectiveness",
      "timing": {
        "within-date-range": {
          "start": "2024-01-01T00:00:00Z",
          "end": "2024-01-31T23:59:59Z"
        }
      },
      "dependencies": [
        {"task-uuid-ref": "task-000-prep"}
      ],
      "subjects": [
        {"subject-uuid-ref": "comp-001-web-server"}
      ],
      "responsible-roles": [
        {"role-id": "security-officer"},
        {"role-id": "system-administrator"}
      ],
      "props": [
        {"name": "workflow-template", "value": "control-review-workflow"},
        {"name": "approval-required", "value": "true"},
        {"name": "automation-level", "value": "semi-automated"}
      ]
    }
  ]
}
```

### Workflow Generation Engine

#### OSCAL to Flowable Translation
```python
class OSCALWorkflowGenerator:
    """
    Generate Flowable workflows from OSCAL assessment tasks and activities
    """
    
    def __init__(self, flowable_client):
        self.flowable_client = flowable_client
        self.workflow_templates = self._load_workflow_templates()
        self.role_mappings = self._load_role_mappings()
    
    def generate_workflow_from_oscal_task(self, oscal_task, assessment_context):
        """
        Generate Flowable workflow definition from OSCAL task
        """
        workflow_definition = {
            "id": f"oscal-task-{oscal_task['uuid']}",
            "name": oscal_task.get("title", "OSCAL Task Workflow"),
            "description": oscal_task.get("description", ""),
            "version": 1,
            "processes": [
                self._create_main_process(oscal_task, assessment_context)
            ]
        }
        
        return workflow_definition
    
    def _create_main_process(self, oscal_task, assessment_context):
        """
        Create main workflow process from OSCAL task
        """
        process = {
            "id": f"process-{oscal_task['uuid']}",
            "name": oscal_task["title"],
            "isExecutable": True,
            "flowElements": []
        }
        
        # Start event
        start_event = self._create_start_event(oscal_task)
        process["flowElements"].append(start_event)
        
        # Task activities
        task_activities = self._create_task_activities(oscal_task, assessment_context)
        process["flowElements"].extend(task_activities)
        
        # Approval activities (if required)
        if self._requires_approval(oscal_task):
            approval_activities = self._create_approval_activities(oscal_task)
            process["flowElements"].extend(approval_activities)
        
        # End event
        end_event = self._create_end_event(oscal_task)
        process["flowElements"].append(end_event)
        
        # Sequence flows
        sequence_flows = self._create_sequence_flows(process["flowElements"])
        process["flowElements"].extend(sequence_flows)
        
        return process
    
    def _create_task_activities(self, oscal_task, assessment_context):
        """
        Create workflow activities from OSCAL task requirements
        """
        activities = []
        
        # Determine task type and create appropriate activities
        task_type = self._determine_task_type(oscal_task)
        
        if task_type == "automated-assessment":
            activities.extend(self._create_automated_assessment_activities(oscal_task))
        elif task_type == "manual-review":
            activities.extend(self._create_manual_review_activities(oscal_task))
        elif task_type == "approval-workflow":
            activities.extend(self._create_approval_workflow_activities(oscal_task))
        elif task_type == "remediation":
            activities.extend(self._create_remediation_activities(oscal_task))
        
        return activities
    
    def _create_automated_assessment_activities(self, oscal_task):
        """
        Create automated assessment workflow activities
        """
        activities = []
        
        # Preparation task
        prep_task = {
            "id": f"prep-{oscal_task['uuid']}",
            "name": "Prepare Assessment Environment",
            "type": "serviceTask",
            "implementation": {
                "type": "class",
                "value": "com.grcos.workflow.PrepareAssessmentTask"
            },
            "inputParameters": {
                "oscalTaskUuid": oscal_task["uuid"],
                "subjects": oscal_task.get("subjects", []),
                "assessmentType": "automated"
            }
        }
        activities.append(prep_task)
        
        # Execute assessment
        execute_task = {
            "id": f"execute-{oscal_task['uuid']}",
            "name": "Execute Automated Assessment",
            "type": "serviceTask",
            "implementation": {
                "type": "class",
                "value": "com.grcos.workflow.ExecuteAssessmentTask"
            },
            "inputParameters": {
                "oscalTaskUuid": oscal_task["uuid"],
                "assessmentTools": self._get_assessment_tools(oscal_task)
            }
        }
        activities.append(execute_task)
        
        # Process results
        process_task = {
            "id": f"process-{oscal_task['uuid']}",
            "name": "Process Assessment Results",
            "type": "serviceTask",
            "implementation": {
                "type": "class",
                "value": "com.grcos.workflow.ProcessResultsTask"
            },
            "inputParameters": {
                "oscalTaskUuid": oscal_task["uuid"],
                "generateFindings": True,
                "updateOscalResults": True
            }
        }
        activities.append(process_task)
        
        return activities
```

## Workflow Orchestration Patterns

### Compliance Workflow Templates

#### Control Implementation Workflow
```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://grcos.com/workflows">
  
  <process id="control-implementation-workflow" name="Control Implementation Workflow" isExecutable="true">
    
    <!-- Start Event -->
    <startEvent id="start" name="Control Implementation Request">
      <extensionElements>
        <flowable:formProperty id="controlId" name="Control ID" type="string" required="true"/>
        <flowable:formProperty id="systemUuid" name="System UUID" type="string" required="true"/>
        <flowable:formProperty id="implementationApproach" name="Implementation Approach" type="enum">
          <flowable:value id="technical" name="Technical Implementation"/>
          <flowable:value id="procedural" name="Procedural Implementation"/>
          <flowable:value id="hybrid" name="Hybrid Implementation"/>
        </flowable:formProperty>
      </extensionElements>
    </startEvent>
    
    <!-- Design Implementation -->
    <userTask id="design-implementation" name="Design Control Implementation" 
              flowable:assignee="${initiator}">
      <documentation>Design the technical and procedural implementation for the control</documentation>
      <extensionElements>
        <flowable:formProperty id="implementationDesign" name="Implementation Design" type="string" required="true"/>
        <flowable:formProperty id="requiredResources" name="Required Resources" type="string"/>
        <flowable:formProperty id="timeline" name="Implementation Timeline" type="date"/>
      </extensionElements>
    </userTask>
    
    <!-- Review and Approval -->
    <userTask id="review-implementation" name="Review Implementation Design"
              flowable:candidateGroups="security-architects">
      <documentation>Review the proposed control implementation design</documentation>
      <extensionElements>
        <flowable:formProperty id="reviewDecision" name="Review Decision" type="enum" required="true">
          <flowable:value id="approved" name="Approved"/>
          <flowable:value id="rejected" name="Rejected"/>
          <flowable:value id="needs-revision" name="Needs Revision"/>
        </flowable:formProperty>
        <flowable:formProperty id="reviewComments" name="Review Comments" type="string"/>
      </extensionElements>
    </userTask>
    
    <!-- Decision Gateway -->
    <exclusiveGateway id="review-decision-gateway" name="Review Decision"/>
    
    <!-- Implement Control -->
    <serviceTask id="implement-control" name="Implement Control"
                 flowable:class="com.grcos.workflow.ImplementControlTask">
      <extensionElements>
        <flowable:field name="oscalService">
          <flowable:expression>${oscalService}</flowable:expression>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Test Implementation -->
    <serviceTask id="test-implementation" name="Test Control Implementation"
                 flowable:class="com.grcos.workflow.TestControlTask">
      <extensionElements>
        <flowable:field name="assessmentService">
          <flowable:expression>${assessmentService}</flowable:expression>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- Update OSCAL SSP -->
    <serviceTask id="update-ssp" name="Update System Security Plan"
                 flowable:class="com.grcos.workflow.UpdateSSPTask">
      <extensionElements>
        <flowable:field name="oscalRepository">
          <flowable:expression>${oscalRepository}</flowable:expression>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    
    <!-- End Event -->
    <endEvent id="end" name="Control Implementation Complete"/>
    
    <!-- Sequence Flows -->
    <sequenceFlow id="flow1" sourceRef="start" targetRef="design-implementation"/>
    <sequenceFlow id="flow2" sourceRef="design-implementation" targetRef="review-implementation"/>
    <sequenceFlow id="flow3" sourceRef="review-implementation" targetRef="review-decision-gateway"/>
    <sequenceFlow id="flow4" sourceRef="review-decision-gateway" targetRef="implement-control">
      <conditionExpression>${reviewDecision == 'approved'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow5" sourceRef="review-decision-gateway" targetRef="design-implementation">
      <conditionExpression>${reviewDecision == 'needs-revision'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow6" sourceRef="implement-control" targetRef="test-implementation"/>
    <sequenceFlow id="flow7" sourceRef="test-implementation" targetRef="update-ssp"/>
    <sequenceFlow id="flow8" sourceRef="update-ssp" targetRef="end"/>
    
  </process>
</definitions>
```

#### Assessment Workflow Template
```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://grcos.com/workflows">
  
  <process id="assessment-workflow" name="Security Assessment Workflow" isExecutable="true">
    
    <!-- Start Event -->
    <startEvent id="start" name="Assessment Scheduled">
      <timerEventDefinition>
        <timeCycle>R/PT24H</timeCycle> <!-- Repeat every 24 hours -->
      </timerEventDefinition>
    </startEvent>
    
    <!-- Check Assessment Requirements -->
    <serviceTask id="check-requirements" name="Check Assessment Requirements"
                 flowable:class="com.grcos.workflow.CheckAssessmentRequirementsTask"/>
    
    <!-- Decision Gateway -->
    <exclusiveGateway id="assessment-needed-gateway" name="Assessment Needed?"/>
    
    <!-- Generate Assessment Plan -->
    <serviceTask id="generate-plan" name="Generate Assessment Plan"
                 flowable:class="com.grcos.workflow.GenerateAssessmentPlanTask"/>
    
    <!-- Execute Automated Tests -->
    <serviceTask id="execute-automated" name="Execute Automated Tests"
                 flowable:class="com.grcos.workflow.ExecuteAutomatedTestsTask"/>
    
    <!-- Schedule Manual Reviews -->
    <userTask id="schedule-manual" name="Schedule Manual Reviews"
              flowable:candidateGroups="assessment-coordinators">
      <documentation>Schedule required manual assessment activities</documentation>
    </userTask>
    
    <!-- Parallel Gateway for Concurrent Activities -->
    <parallelGateway id="parallel-start" name="Start Parallel Activities"/>
    
    <!-- Vulnerability Scanning -->
    <serviceTask id="vulnerability-scan" name="Vulnerability Scanning"
                 flowable:class="com.grcos.workflow.VulnerabilityScanTask"/>
    
    <!-- Configuration Assessment -->
    <serviceTask id="config-assessment" name="Configuration Assessment"
                 flowable:class="com.grcos.workflow.ConfigurationAssessmentTask"/>
    
    <!-- Manual Testing -->
    <userTask id="manual-testing" name="Manual Testing Activities"
              flowable:candidateGroups="security-testers"/>
    
    <!-- Parallel Gateway for Joining -->
    <parallelGateway id="parallel-end" name="Join Parallel Activities"/>
    
    <!-- Analyze Results -->
    <serviceTask id="analyze-results" name="Analyze Assessment Results"
                 flowable:class="com.grcos.workflow.AnalyzeResultsTask"/>
    
    <!-- Generate Findings -->
    <serviceTask id="generate-findings" name="Generate Findings"
                 flowable:class="com.grcos.workflow.GenerateFindingsTask"/>
    
    <!-- Update OSCAL Results -->
    <serviceTask id="update-results" name="Update OSCAL Assessment Results"
                 flowable:class="com.grcos.workflow.UpdateOSCALResultsTask"/>
    
    <!-- End Event -->
    <endEvent id="end" name="Assessment Complete"/>
    
    <!-- No Assessment End -->
    <endEvent id="no-assessment-end" name="No Assessment Needed"/>
    
    <!-- Sequence Flows -->
    <sequenceFlow id="flow1" sourceRef="start" targetRef="check-requirements"/>
    <sequenceFlow id="flow2" sourceRef="check-requirements" targetRef="assessment-needed-gateway"/>
    <sequenceFlow id="flow3" sourceRef="assessment-needed-gateway" targetRef="generate-plan">
      <conditionExpression>${assessmentNeeded == true}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow4" sourceRef="assessment-needed-gateway" targetRef="no-assessment-end">
      <conditionExpression>${assessmentNeeded == false}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow5" sourceRef="generate-plan" targetRef="execute-automated"/>
    <sequenceFlow id="flow6" sourceRef="execute-automated" targetRef="schedule-manual"/>
    <sequenceFlow id="flow7" sourceRef="schedule-manual" targetRef="parallel-start"/>
    <sequenceFlow id="flow8" sourceRef="parallel-start" targetRef="vulnerability-scan"/>
    <sequenceFlow id="flow9" sourceRef="parallel-start" targetRef="config-assessment"/>
    <sequenceFlow id="flow10" sourceRef="parallel-start" targetRef="manual-testing"/>
    <sequenceFlow id="flow11" sourceRef="vulnerability-scan" targetRef="parallel-end"/>
    <sequenceFlow id="flow12" sourceRef="config-assessment" targetRef="parallel-end"/>
    <sequenceFlow id="flow13" sourceRef="manual-testing" targetRef="parallel-end"/>
    <sequenceFlow id="flow14" sourceRef="parallel-end" targetRef="analyze-results"/>
    <sequenceFlow id="flow15" sourceRef="analyze-results" targetRef="generate-findings"/>
    <sequenceFlow id="flow16" sourceRef="generate-findings" targetRef="update-results"/>
    <sequenceFlow id="flow17" sourceRef="update-results" targetRef="end"/>
    
  </process>
</definitions>
```

## Workflow Agent Integration

### Intelligent Workflow Optimization

#### Workflow Performance Analyzer
```python
class WorkflowPerformanceAnalyzer:
    """
    Analyze workflow performance and optimize based on OSCAL data
    """
    
    def __init__(self, flowable_client, oscal_repository):
        self.flowable_client = flowable_client
        self.oscal_repository = oscal_repository
        self.performance_metrics = {}
    
    def analyze_workflow_performance(self, workflow_id, time_period):
        """
        Analyze workflow performance metrics
        """
        # Get workflow execution history
        executions = self.flowable_client.get_workflow_executions(
            workflow_id, 
            time_period
        )
        
        performance_analysis = {
            "workflow_id": workflow_id,
            "analysis_period": time_period,
            "total_executions": len(executions),
            "average_duration": self._calculate_average_duration(executions),
            "success_rate": self._calculate_success_rate(executions),
            "bottlenecks": self._identify_bottlenecks(executions),
            "optimization_recommendations": []
        }
        
        # Analyze task-level performance
        task_performance = self._analyze_task_performance(executions)
        performance_analysis["task_performance"] = task_performance
        
        # Generate optimization recommendations
        recommendations = self._generate_optimization_recommendations(
            performance_analysis
        )
        performance_analysis["optimization_recommendations"] = recommendations
        
        return performance_analysis
    
    def optimize_workflow_based_on_oscal(self, workflow_id, oscal_assessment_results):
        """
        Optimize workflow based on OSCAL assessment results
        """
        # Analyze assessment patterns
        assessment_patterns = self._analyze_assessment_patterns(oscal_assessment_results)
        
        # Identify workflow optimization opportunities
        optimizations = []
        
        # Automate repetitive tasks
        repetitive_tasks = self._identify_repetitive_tasks(assessment_patterns)
        for task in repetitive_tasks:
            optimization = {
                "type": "automation",
                "task": task,
                "recommendation": "Convert manual task to automated service task",
                "expected_improvement": "50% reduction in execution time"
            }
            optimizations.append(optimization)
        
        # Optimize approval chains
        approval_bottlenecks = self._identify_approval_bottlenecks(assessment_patterns)
        for bottleneck in approval_bottlenecks:
            optimization = {
                "type": "approval_optimization",
                "bottleneck": bottleneck,
                "recommendation": "Implement parallel approval or delegation rules",
                "expected_improvement": "30% reduction in approval time"
            }
            optimizations.append(optimization)
        
        # Implement optimizations
        for optimization in optimizations:
            self._implement_workflow_optimization(workflow_id, optimization)
        
        return optimizations
```

### Event-Driven Workflow Triggers

#### OSCAL Event to Workflow Mapping
```python
class OSCALEventWorkflowTrigger:
    """
    Trigger workflows based on OSCAL document events
    """
    
    def __init__(self, flowable_client, event_processor):
        self.flowable_client = flowable_client
        self.event_processor = event_processor
        self.trigger_mappings = self._load_trigger_mappings()
    
    def process_oscal_event(self, event):
        """
        Process OSCAL event and trigger appropriate workflows
        """
        event_type = event.get("eventType")
        event_data = event.get("data", {})
        
        # Find matching workflow triggers
        matching_triggers = self._find_matching_triggers(event_type, event_data)
        
        triggered_workflows = []
        
        for trigger in matching_triggers:
            try:
                workflow_instance = self._start_workflow(trigger, event_data)
                triggered_workflows.append(workflow_instance)
                
                # Log workflow trigger
                self._log_workflow_trigger(event, trigger, workflow_instance)
                
            except Exception as e:
                self._log_trigger_error(event, trigger, str(e))
        
        return triggered_workflows
    
    def _find_matching_triggers(self, event_type, event_data):
        """
        Find workflow triggers that match the OSCAL event
        """
        matching_triggers = []
        
        for trigger in self.trigger_mappings:
            if self._event_matches_trigger(event_type, event_data, trigger):
                matching_triggers.append(trigger)
        
        return matching_triggers
    
    def _event_matches_trigger(self, event_type, event_data, trigger):
        """
        Check if OSCAL event matches workflow trigger criteria
        """
        # Check event type match
        if trigger["event_type"] != event_type:
            return False
        
        # Check additional conditions
        conditions = trigger.get("conditions", [])
        for condition in conditions:
            if not self._evaluate_condition(condition, event_data):
                return False
        
        return True
    
    def _start_workflow(self, trigger, event_data):
        """
        Start workflow instance based on trigger configuration
        """
        workflow_variables = self._prepare_workflow_variables(trigger, event_data)
        
        workflow_instance = self.flowable_client.start_process_instance(
            process_definition_key=trigger["workflow_id"],
            variables=workflow_variables,
            business_key=f"oscal-{event_data.get('uuid', 'unknown')}"
        )
        
        return workflow_instance
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Workflows Module Team
