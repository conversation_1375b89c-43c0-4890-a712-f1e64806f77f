---
name: Bug report
about: Report a problem for the OPA community to investigate
title: ''
labels: bug
assignees: ''

---

<!-- Thanks for opening an issue to request a feature or file a bug!
If you provide some basic information, it helps us address problems faster. -->

## Short description 
<!--
Any information you think might be helpful. Examples include the environment
where OPA was running (e.g., if inside Kubernetes, what resource limits did you configure
OPA with?), how long OPA had been running for, what was happening around the time
when you identified the problem, etc.

Examples:

* OPA version
* Example query, input, data, and policy that OPA was given
* Example output that OPA returned
* For server and CLI, the flags/configuration that you provided to OPA
* For server, any relevant log messages from OPA
* For Go and Wasm, the arguments you invoked OPA with
-->

## Steps To Reproduce
<!--
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See an error
-->

## Expected behavior
<!--
Describe what you expected to happen.
-->

## Additional context
<!--
Add any other context about the problem here.
-->
