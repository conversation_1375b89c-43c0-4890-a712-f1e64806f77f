name: "Check Broken Links"

on:
  repository_dispatch:
  workflow_dispatch:
  schedule:
    - cron: "0 0 1 * *" # At 00:00 on day-of-month 1

jobs:
  linkChecker:
    runs-on: ubuntu-latest
    permissions:
      issues: write # required for peter-evans/create-issue-from-file
    steps:
      - uses: actions/checkout@v4

      - name: Link Checker
        id: lychee
        uses: lycheeverse/lychee-action@v2
        with:
          args: --max-concurrency 1 --no-progress --exclude-path vendor --exclude-path CHANGELOG.md --scheme https --scheme http --accept 200..=206,403,429  .
          fail: false

      - name: Create Issue From File
        if: steps.lychee.outputs.exit_code != 0
        uses: peter-evans/create-issue-from-file@v5
        with:
          title: Link Checker Report
          content-filepath: ./lychee/out.md
          labels: report, automated issue
