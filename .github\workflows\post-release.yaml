name: Post Release

on:
  release:
    types: [published]

permissions:
  contents: none

jobs:
  kick-netlify:
    name: Kick Netlify
    runs-on: ubuntu-24.04
    steps:
      - name: Trigger Netlify Deploy
        env:
          NETLIFY_BUILD_HOOK_URL: ${{ secrets.NETLIFY_BUILD_HOOK_URL }}
        if: ${{ env.NETLIFY_BUILD_HOOK_URL }}
        run: |
          curl --fail --request POST -d {} ${{ env.NETLIFY_BUILD_HOOK_URL }}
