# GRCOS OSCAL Integration Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying OSCAL integration within the GRCOS platform. It covers infrastructure setup, service configuration, blockchain network deployment, and operational procedures for production environments.

## Prerequisites

### System Requirements

#### Minimum Hardware Requirements
```
Production Environment:
- CPU: 16 cores (Intel Xeon or AMD EPYC)
- RAM: 64 GB
- Storage: 1 TB NVMe SSD (primary), 5 TB HDD (blockchain/archive)
- Network: 10 Gbps Ethernet

Development Environment:
- CPU: 8 cores
- RAM: 32 GB
- Storage: 500 GB SSD
- Network: 1 Gbps Ethernet
```

#### Software Dependencies
```
Operating System: Ubuntu 22.04 LTS or RHEL 8+
Container Runtime: Docker 24.0+ and Kubernetes 1.28+
Database: MongoDB 7.0+, Redis 7.0+
Message Queue: Apache Kafka 3.5+
Blockchain: Hyperledger Fabric 2.5+
AI Framework: Python 3.11+, CrewAI 0.28+
```

### Network Architecture

#### Network Topology
```
Internet → Load Balancer → API Gateway → GRCOS Services → Database Cluster
                                    ↓
                              Blockchain Network
                                    ↓
                              Message Queue Cluster
```

#### Port Configuration
```
Service Ports:
- API Gateway: 443 (HTTPS), 80 (HTTP redirect)
- OSCAL Service: 8080
- Blockchain Peers: 7051, 7052, 7053
- Blockchain Orderers: 7050
- MongoDB: 27017
- Redis: 6379
- Kafka: 9092
```

## Infrastructure Deployment

### Kubernetes Cluster Setup

#### Cluster Configuration
```yaml
# k8s-cluster-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: grcos-cluster-config
  namespace: grcos-system
data:
  cluster-name: "grcos-production"
  region: "us-east-1"
  availability-zones: "us-east-1a,us-east-1b,us-east-1c"
  node-groups: |
    - name: grcos-workers
      instance-type: m5.2xlarge
      min-size: 3
      max-size: 10
      desired-capacity: 5
    - name: blockchain-nodes
      instance-type: c5.xlarge
      min-size: 3
      max-size: 6
      desired-capacity: 3
```

#### Namespace Setup
```bash
#!/bin/bash
# setup-namespaces.sh

# Create GRCOS namespaces
kubectl create namespace grcos-system
kubectl create namespace grcos-services
kubectl create namespace grcos-blockchain
kubectl create namespace grcos-monitoring

# Apply resource quotas
kubectl apply -f - <<EOF
apiVersion: v1
kind: ResourceQuota
metadata:
  name: grcos-services-quota
  namespace: grcos-services
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    persistentvolumeclaims: "10"
EOF
```

### Database Deployment

#### MongoDB Cluster Setup
```yaml
# mongodb-deployment.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb-cluster
  namespace: grcos-services
spec:
  serviceName: mongodb-service
  replicas: 3
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: mongo:7.0
        ports:
        - containerPort: 27017
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: password
        volumeMounts:
        - name: mongodb-storage
          mountPath: /data/db
        - name: mongodb-config
          mountPath: /etc/mongo
  volumeClaimTemplates:
  - metadata:
      name: mongodb-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 100Gi
      storageClassName: fast-ssd
```

#### Redis Cluster Setup
```yaml
# redis-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-cluster
  namespace: grcos-services
spec:
  replicas: 3
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7.0-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - /etc/redis/redis.conf
        volumeMounts:
        - name: redis-config
          mountPath: /etc/redis
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-config
        configMap:
          name: redis-config
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc
```

### Blockchain Network Deployment

#### Hyperledger Fabric Network Setup
```bash
#!/bin/bash
# deploy-blockchain.sh

# Set environment variables
export FABRIC_VERSION=2.5.4
export FABRIC_CA_VERSION=1.5.7
export GRCOS_NETWORK_NAME=grcos-network

# Create blockchain namespace
kubectl create namespace grcos-blockchain

# Deploy Certificate Authority
kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ca-grcos-org
  namespace: grcos-blockchain
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ca-grcos-org
  template:
    metadata:
      labels:
        app: ca-grcos-org
    spec:
      containers:
      - name: ca
        image: hyperledger/fabric-ca:${FABRIC_CA_VERSION}
        ports:
        - containerPort: 7054
        env:
        - name: FABRIC_CA_HOME
          value: /etc/hyperledger/fabric-ca-server
        - name: FABRIC_CA_SERVER_CA_NAME
          value: ca-grcos-org
        - name: FABRIC_CA_SERVER_TLS_ENABLED
          value: "true"
        volumeMounts:
        - name: ca-storage
          mountPath: /etc/hyperledger/fabric-ca-server
      volumes:
      - name: ca-storage
        persistentVolumeClaim:
          claimName: ca-pvc
EOF

# Deploy Peer Nodes
for i in {0..2}; do
kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: peer${i}-grcos-org
  namespace: grcos-blockchain
spec:
  replicas: 1
  selector:
    matchLabels:
      app: peer${i}-grcos-org
  template:
    metadata:
      labels:
        app: peer${i}-grcos-org
    spec:
      containers:
      - name: peer
        image: hyperledger/fabric-peer:${FABRIC_VERSION}
        ports:
        - containerPort: 7051
        - containerPort: 7052
        env:
        - name: CORE_PEER_ID
          value: peer${i}.grcos.org
        - name: CORE_PEER_ADDRESS
          value: peer${i}-grcos-org:7051
        - name: CORE_PEER_LISTENADDRESS
          value: 0.0.0.0:7051
        - name: CORE_PEER_CHAINCODEADDRESS
          value: peer${i}-grcos-org:7052
        - name: CORE_PEER_CHAINCODELISTENADDRESS
          value: 0.0.0.0:7052
        - name: CORE_PEER_GOSSIP_BOOTSTRAP
          value: peer0-grcos-org:7051
        - name: CORE_PEER_GOSSIP_EXTERNALENDPOINT
          value: peer${i}-grcos-org:7051
        - name: CORE_PEER_LOCALMSPID
          value: GrcosOrgMSP
        - name: CORE_VM_ENDPOINT
          value: unix:///host/var/run/docker.sock
        - name: CORE_VM_DOCKER_HOSTCONFIG_NETWORKMODE
          value: grcos-blockchain
        - name: FABRIC_LOGGING_SPEC
          value: INFO
        - name: CORE_PEER_TLS_ENABLED
          value: "true"
        volumeMounts:
        - name: peer-storage
          mountPath: /var/hyperledger/production
        - name: docker-socket
          mountPath: /host/var/run/docker.sock
      volumes:
      - name: peer-storage
        persistentVolumeClaim:
          claimName: peer${i}-pvc
      - name: docker-socket
        hostPath:
          path: /var/run/docker.sock
EOF
done
```

#### Smart Contract Deployment
```bash
#!/bin/bash
# deploy-chaincode.sh

# Package OSCAL compliance chaincode
peer lifecycle chaincode package oscal-compliance.tar.gz \
  --path ./chaincode/oscal-compliance \
  --lang golang \
  --label oscal-compliance_1.0

# Install chaincode on all peers
for i in {0..2}; do
  peer lifecycle chaincode install oscal-compliance.tar.gz \
    --peerAddresses peer${i}-grcos-org:7051 \
    --tlsRootCertFiles /etc/hyperledger/fabric/tls/ca.crt
done

# Approve chaincode definition
peer lifecycle chaincode approveformyorg \
  --channelID compliance-channel \
  --name oscal-compliance \
  --version 1.0 \
  --package-id $(peer lifecycle chaincode queryinstalled --output json | jq -r '.installed_chaincodes[0].package_id') \
  --sequence 1 \
  --tls \
  --cafile /etc/hyperledger/fabric/tls/ca.crt

# Commit chaincode definition
peer lifecycle chaincode commit \
  --channelID compliance-channel \
  --name oscal-compliance \
  --version 1.0 \
  --sequence 1 \
  --tls \
  --cafile /etc/hyperledger/fabric/tls/ca.crt \
  --peerAddresses peer0-grcos-org:7051 \
  --tlsRootCertFiles /etc/hyperledger/fabric/tls/ca.crt
```

## GRCOS Services Deployment

### OSCAL Service Deployment

#### Service Configuration
```yaml
# oscal-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grcos-oscal-service
  namespace: grcos-services
spec:
  replicas: 3
  selector:
    matchLabels:
      app: grcos-oscal-service
  template:
    metadata:
      labels:
        app: grcos-oscal-service
    spec:
      containers:
      - name: oscal-service
        image: grcos/oscal-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: mongodb-uri
        - name: REDIS_URI
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: redis-uri
        - name: BLOCKCHAIN_NETWORK_CONFIG
          value: /etc/blockchain/network-config.yaml
        - name: OSCAL_SCHEMA_PATH
          value: /etc/oscal/schemas
        volumeMounts:
        - name: blockchain-config
          mountPath: /etc/blockchain
        - name: oscal-schemas
          mountPath: /etc/oscal/schemas
        - name: app-config
          mountPath: /etc/grcos
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: blockchain-config
        configMap:
          name: blockchain-config
      - name: oscal-schemas
        configMap:
          name: oscal-schemas
      - name: app-config
        configMap:
          name: grcos-config
```

#### Service Configuration
```yaml
# oscal-service-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: grcos-config
  namespace: grcos-services
data:
  application.yaml: |
    server:
      port: 8080
    
    grcos:
      oscal:
        validation:
          enabled: true
          schema-path: /etc/oscal/schemas
        blockchain:
          enabled: true
          network-config: /etc/blockchain/network-config.yaml
        cache:
          enabled: true
          ttl: 3600
      
      database:
        mongodb:
          database: grcos_oscal
          collection: oscal_documents
        redis:
          database: 0
          key-prefix: "grcos:oscal:"
      
      security:
        encryption:
          algorithm: AES-256-GCM
          key-rotation-interval: 30d
        authentication:
          jwt:
            secret-key: ${JWT_SECRET_KEY}
            expiration: 24h
```

### AI Agent Deployment

#### Agent Service Deployment
```yaml
# ai-agents-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grcos-ai-agents
  namespace: grcos-services
spec:
  replicas: 2
  selector:
    matchLabels:
      app: grcos-ai-agents
  template:
    metadata:
      labels:
        app: grcos-ai-agents
    spec:
      containers:
      - name: system-agent
        image: grcos/system-agent:latest
        env:
        - name: AGENT_TYPE
          value: "system"
        - name: CREWAI_CONFIG
          value: /etc/crewai/system-agent-config.yaml
        volumeMounts:
        - name: agent-config
          mountPath: /etc/crewai
      - name: compliance-agent
        image: grcos/compliance-agent:latest
        env:
        - name: AGENT_TYPE
          value: "compliance"
        - name: CREWAI_CONFIG
          value: /etc/crewai/compliance-agent-config.yaml
        volumeMounts:
        - name: agent-config
          mountPath: /etc/crewai
      - name: assessment-agent
        image: grcos/assessment-agent:latest
        env:
        - name: AGENT_TYPE
          value: "assessment"
        - name: CREWAI_CONFIG
          value: /etc/crewai/assessment-agent-config.yaml
        volumeMounts:
        - name: agent-config
          mountPath: /etc/crewai
      volumes:
      - name: agent-config
        configMap:
          name: ai-agent-config
```

## Configuration Management

### Environment Configuration

#### Production Environment Setup
```bash
#!/bin/bash
# setup-production-env.sh

# Create production configuration
kubectl create secret generic database-secret \
  --namespace=grcos-services \
  --from-literal=mongodb-uri="mongodb://admin:${MONGODB_PASSWORD}@mongodb-service:27017/grcos_oscal?authSource=admin" \
  --from-literal=redis-uri="redis://redis-service:6379/0"

kubectl create secret generic blockchain-secret \
  --namespace=grcos-services \
  --from-literal=peer-tls-cert="$(cat /path/to/peer-tls.crt)" \
  --from-literal=peer-tls-key="$(cat /path/to/peer-tls.key)" \
  --from-literal=ca-cert="$(cat /path/to/ca.crt)"

kubectl create secret generic app-secret \
  --namespace=grcos-services \
  --from-literal=jwt-secret-key="${JWT_SECRET_KEY}" \
  --from-literal=encryption-key="${ENCRYPTION_KEY}"

# Apply production configurations
kubectl apply -f production-configs/
```

#### SSL/TLS Certificate Setup
```bash
#!/bin/bash
# setup-tls.sh

# Generate TLS certificates using cert-manager
kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: grcos-tls-cert
  namespace: grcos-services
spec:
  secretName: grcos-tls-secret
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - grcos.example.com
  - api.grcos.example.com
  - blockchain.grcos.example.com
EOF
```

### Monitoring and Logging Setup

#### Prometheus Monitoring
```yaml
# monitoring-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: grcos-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus
        - name: prometheus-storage
          mountPath: /prometheus
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-storage
        persistentVolumeClaim:
          claimName: prometheus-pvc
```

## Deployment Verification

### Health Check Procedures

#### Service Health Verification
```bash
#!/bin/bash
# verify-deployment.sh

echo "Verifying GRCOS OSCAL deployment..."

# Check service health
kubectl get pods -n grcos-services
kubectl get services -n grcos-services

# Test OSCAL service endpoint
curl -k https://api.grcos.example.com/health

# Test blockchain connectivity
kubectl exec -n grcos-blockchain peer0-grcos-org-0 -- peer channel list

# Verify database connectivity
kubectl exec -n grcos-services mongodb-cluster-0 -- mongo --eval "db.adminCommand('ismaster')"

# Test AI agent communication
kubectl logs -n grcos-services deployment/grcos-ai-agents -c system-agent

echo "Deployment verification complete."
```

#### Performance Testing
```bash
#!/bin/bash
# performance-test.sh

# Load test OSCAL API endpoints
k6 run --vus 50 --duration 5m performance-tests/oscal-api-test.js

# Test blockchain transaction throughput
kubectl exec -n grcos-blockchain peer0-grcos-org-0 -- peer chaincode invoke \
  -o orderer0-grcos-org:7050 \
  -C compliance-channel \
  -n oscal-compliance \
  -c '{"function":"RegisterOSCALDocument","Args":["test-doc","catalog","test-uuid","test-hash","test-metadata"]}'

# Monitor resource usage
kubectl top nodes
kubectl top pods -n grcos-services
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS DevOps Team
