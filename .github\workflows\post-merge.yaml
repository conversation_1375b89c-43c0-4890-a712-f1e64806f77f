name: Post Merge

on:
  push:
    branches:
      - main

permissions:  
  contents: read

jobs:
  generate:
    permissions:
      contents: write  # for Git to git push
    name: Sync Generated Code and Docs
    runs-on: ubuntu-24.04
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          token: ${{ secrets.GH_PUSH_TOKEN }} # required to push to protected branch below

      - name: Generate
        run: make clean generate docs-generate-cli-docs

      - name: Commit & Push
        shell: bash
        run: |
          # Commit any changes and push as needed.

          # See https://github.com/actions/checkout#push-a-commit-using-the-built-in-token
          AUTHOR=wasm-updater
          git config user.name ${AUTHOR}
          git config user.email ${AUTHOR}@github.com

          # Prevent looping if the build was non-deterministic..
          CAN_PUSH=1
          if [[ "$(git log -1 --pretty=format:'%an')" == "${AUTHOR}" ]]; then
              CAN_PUSH=0
          fi

          if ./build/commit-wasm-bins.sh; then
            if [[ "${CAN_PUSH}" == "1" ]]; then
              git push
            else
              echo "Previous commit was auto-generated -- Aborting!"
              exit 1
            fi
          else
            echo "No generated changes to push!"
          fi

          AUTHOR=cli-docs-updater
          git config user.name ${AUTHOR}
          git config user.email ${AUTHOR}@github.com

          # Prevent looping if the build was non-deterministic..
          CAN_PUSH=1
          if [[ "$(git log -1 --pretty=format:'%an')" == "${AUTHOR}" ]]; then
              CAN_PUSH=0
          fi

          if ./build/commit-cli-docs.sh; then
            if [[ "${CAN_PUSH}" == "1" ]]; then
              git push
            else
              echo "Previous commit was auto-generated -- Aborting!"
              exit 1
            fi
          else
            echo "No generated changes to push!"
          fi

  code-coverage:
    name: Update Go Test Coverage
    runs-on: ubuntu-24.04
    needs: generate
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Unit Test Golang
        run: make ci-go-test-coverage
        timeout-minutes: 30

  release-build:
    name: Release Build (linux, windows)
    runs-on: ubuntu-24.04
    needs: generate
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: Git Describe
        run: git describe --tags

      - name: Build Linux and Windows
        run: make ci-go-ci-build-linux ci-go-ci-build-linux-static ci-go-ci-build-windows
        timeout-minutes: 30
        env:
          TELEMETRY_URL: ${{ secrets.TELEMETRY_URL }}

      - name: Build Linux arm64
        run: make ci-go-ci-build-linux-static
        timeout-minutes: 30
        env:
          GOARCH: arm64
          TELEMETRY_URL: ${{ secrets.TELEMETRY_URL }}

      - name: Upload binaries
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        if: always()
        with:
          name: binaries-linux-windows
          path: _release

  release-build-darwin:
    name: Release Build (darwin)
    runs-on: macos-14
    needs: generate
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: Git Describe
        run: git describe --tags

      - id: go_version
        name: Read go version
        run: echo "go_version=$(cat .go-version)" >> $GITHUB_OUTPUT

      - name: Install Go (${{ steps.go_version.outputs.go_version }})
        uses: actions/setup-go@0aaccfd150d50ccaeb58ebd88d36e91967a5f35b # v5.4.0
        with:
          go-version: ${{ steps.go_version.outputs.go_version }}

      - name: Build Darwin
        run: |
          make ci-build-darwin GOARCH=amd64
          make ci-build-darwin-arm64-static
        timeout-minutes: 30
        env:
          TELEMETRY_URL: ${{ secrets.TELEMETRY_URL }}

      - name: Upload binaries (darwin)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        if: always()
        with:
          name: binaries-darwin
          path: _release

  deploy-edge:
    name: Push Edge Release
    runs-on: ubuntu-24.04
    needs: [release-build, release-build-darwin]
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Test
        run: make ci-release-test
        timeout-minutes: 60

      - name: Download release binaries
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          pattern: binaries-*
          merge-multiple: true
          path: _release

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@b5ca514318bd6ebac0fb2aedd5d36ec1b5c232a2 # v3.10.0

      - name: Deploy OPA Edge
        env:
          DOCKER_USER: ${{ secrets.DOCKER_USER }}
          DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          DOCKER_IMAGE: ${{ secrets.DOCKER_IMAGE }}
          S3_RELEASE_BUCKET: ${{ secrets.S3_RELEASE_BUCKET }}
        # Only run if required secrets are provided
        if: ${{ env.S3_RELEASE_BUCKET && env.AWS_ACCESS_KEY_ID && env.AWS_SECRET_ACCESS_KEY && env.DOCKER_USER && env.DOCKER_PASSWORD }}
        run: make deploy-ci

  deploy-wasm-builder:
    name: Deploy WASM Builder
    runs-on: ubuntu-24.04
    needs: generate
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Build and Push opa-wasm-builder
        env:
          DOCKER_USER: ${{ secrets.DOCKER_USER }}
          DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
          DOCKER_WASM_BUILDER_IMAGE: ${{ secrets.DOCKER_WASM_BUILDER_IMAGE }}
        # Only run if required secrets are provided
        if: ${{ env.DOCKER_USER && env.DOCKER_PASSWORD }}
        run: make push-wasm-builder-image

  website-smoke-test:
    name : Website Smoke Test
    runs-on: ubuntu-24.04
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Run Smoke Test
        run: make -C docs smoke-test
