# GRCOS OSCAL API Integration Patterns

## Overview

This document defines comprehensive API integration patterns for OSCAL document management within the GRCOS platform. It covers RESTful service design, GraphQL interfaces, event-driven APIs, and integration patterns for external systems consuming and producing OSCAL content.

## RESTful OSCAL API Design

### Core API Architecture

#### Base API Structure
```
Base URL: https://api.grcos.example.com/api/v1
Authentication: <PERSON>er <PERSON> (JWT)
Content-Type: application/json
OSCAL-Version: 1.1.3
```

#### Resource Hierarchy
```
/oscal/
├── catalogs/                    # OSCAL Catalog management
├── profiles/                    # OSCAL Profile management  
├── components/                  # Component Definition management
├── systems/                     # System Security Plan management
├── assessments/                 # Assessment Plan/Results management
├── poams/                       # Plan of Action and Milestones
├── validation/                  # Document validation services
├── conversion/                  # Format conversion services
└── blockchain/                  # Blockchain verification services
```

### OSCAL Catalog API

#### Catalog Management Endpoints
```http
# Create new catalog
POST /api/v1/oscal/catalogs
Content-Type: application/json

{
  "catalog": {
    "uuid": "550e8400-e29b-41d4-a716-************",
    "metadata": {
      "title": "Custom Security Framework",
      "version": "1.0.0",
      "oscal-version": "1.1.3"
    },
    "groups": [...],
    "controls": [...]
  }
}

# Response
HTTP/1.1 201 Created
Location: /api/v1/oscal/catalogs/550e8400-e29b-41d4-a716-************
{
  "catalog_uuid": "550e8400-e29b-41d4-a716-************",
  "status": "created",
  "blockchain_hash": "sha256:abc123...",
  "blockchain_tx_id": "tx789012345"
}
```

```http
# Retrieve catalog
GET /api/v1/oscal/catalogs/550e8400-e29b-41d4-a716-************
Accept: application/json

# Response
HTTP/1.1 200 OK
{
  "catalog": {
    "uuid": "550e8400-e29b-41d4-a716-************",
    "metadata": {...},
    "groups": [...],
    "controls": [...]
  },
  "blockchain_verification": {
    "verified": true,
    "hash": "sha256:abc123...",
    "last_verified": "2024-01-15T14:30:00Z"
  }
}
```

```http
# Update catalog
PUT /api/v1/oscal/catalogs/550e8400-e29b-41d4-a716-************
Content-Type: application/json

{
  "catalog": {
    "uuid": "550e8400-e29b-41d4-a716-************",
    "metadata": {
      "version": "1.1.0",
      "last-modified": "2024-01-16T10:00:00Z"
    }
  },
  "change_reason": "Updated control guidance"
}

# Response
HTTP/1.1 200 OK
{
  "catalog_uuid": "550e8400-e29b-41d4-a716-************",
  "status": "updated",
  "previous_hash": "sha256:abc123...",
  "new_hash": "sha256:def456...",
  "blockchain_tx_id": "tx890123456"
}
```

#### Catalog Query and Search
```http
# Search catalogs
GET /api/v1/oscal/catalogs?search=NIST&framework=security&version=latest
Accept: application/json

# Response
HTTP/1.1 200 OK
{
  "catalogs": [
    {
      "uuid": "nist-800-53-rev5",
      "title": "NIST SP 800-53 Rev 5",
      "version": "5.1.1",
      "framework_type": "security",
      "last_modified": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 1,
    "total_pages": 1
  }
}
```

```http
# Get specific control from catalog
GET /api/v1/oscal/catalogs/nist-800-53-rev5/controls/ac-2
Accept: application/json

# Response
HTTP/1.1 200 OK
{
  "control": {
    "id": "ac-2",
    "title": "Account Management",
    "parts": [...],
    "props": [...],
    "links": [...]
  },
  "catalog_context": {
    "catalog_uuid": "nist-800-53-rev5",
    "group_id": "ac",
    "group_title": "Access Control"
  }
}
```

### System Security Plan API

#### SSP Management
```http
# Create SSP
POST /api/v1/oscal/systems
Content-Type: application/json

{
  "system-security-plan": {
    "uuid": "ssp-prod-001",
    "metadata": {
      "title": "Production System SSP",
      "version": "1.0.0"
    },
    "import-profile": {
      "href": "#grcos-high-baseline"
    },
    "system-characteristics": {...},
    "system-implementation": {...},
    "control-implementation": {...}
  }
}
```

```http
# Get SSP with control implementation status
GET /api/v1/oscal/systems/ssp-prod-001?include=implementation-status
Accept: application/json

# Response
HTTP/1.1 200 OK
{
  "system-security-plan": {...},
  "implementation_summary": {
    "total_controls": 156,
    "implemented": 142,
    "partially_implemented": 12,
    "planned": 2,
    "not_applicable": 0,
    "compliance_percentage": 91.0
  },
  "control_status": [
    {
      "control_id": "ac-1",
      "implementation_status": "implemented",
      "last_tested": "2024-01-15T10:00:00Z",
      "test_result": "passed"
    }
  ]
}
```

### Assessment API

#### Assessment Plan and Results
```http
# Create assessment plan
POST /api/v1/oscal/assessments/plans
Content-Type: application/json

{
  "assessment-plan": {
    "uuid": "ap-q1-2024",
    "metadata": {...},
    "import-ssp": {"href": "#ssp-prod-001"},
    "reviewed-controls": {...},
    "tasks": [...]
  }
}
```

```http
# Execute assessment
POST /api/v1/oscal/assessments/plans/ap-q1-2024/execute
Content-Type: application/json

{
  "execution_options": {
    "automated_only": false,
    "parallel_execution": true,
    "max_duration": "4h"
  }
}

# Response
HTTP/1.1 202 Accepted
{
  "execution_id": "exec-001",
  "status": "running",
  "estimated_completion": "2024-01-20T16:00:00Z",
  "progress_url": "/api/v1/oscal/assessments/executions/exec-001/progress"
}
```

```http
# Get assessment results
GET /api/v1/oscal/assessments/results/ar-q1-2024
Accept: application/json

# Response
HTTP/1.1 200 OK
{
  "assessment-results": {
    "uuid": "ar-q1-2024",
    "metadata": {...},
    "results": [...],
    "observations": [...],
    "findings": [...],
    "risks": [...]
  },
  "summary": {
    "overall_score": 85,
    "findings_count": {
      "critical": 0,
      "high": 2,
      "medium": 5,
      "low": 8
    },
    "risk_level": "medium"
  }
}
```

## GraphQL API Interface

### OSCAL GraphQL Schema

#### Core Types Definition
```graphql
type OSCALCatalog {
  uuid: ID!
  metadata: OSCALMetadata!
  groups: [ControlGroup!]!
  controls: [Control!]!
  backMatter: BackMatter
  blockchainVerification: BlockchainVerification
}

type OSCALMetadata {
  title: String!
  version: String!
  oscalVersion: String!
  lastModified: DateTime!
  published: DateTime
  props: [Property!]!
  links: [Link!]!
  responsibleParties: [ResponsibleParty!]!
}

type Control {
  id: ID!
  title: String!
  parts: [Part!]!
  props: [Property!]!
  links: [Link!]!
  controls: [Control!]!
  implementationStatus: ImplementationStatus
  testResults: [TestResult!]!
}

type SystemSecurityPlan {
  uuid: ID!
  metadata: OSCALMetadata!
  importProfile: ImportProfile!
  systemCharacteristics: SystemCharacteristics!
  systemImplementation: SystemImplementation!
  controlImplementation: ControlImplementation!
  complianceSummary: ComplianceSummary
}

type AssessmentResults {
  uuid: ID!
  metadata: OSCALMetadata!
  importAP: ImportAP!
  results: [Result!]!
  observations: [Observation!]!
  findings: [Finding!]!
  risks: [Risk!]!
  summary: AssessmentSummary
}
```

#### Query Operations
```graphql
type Query {
  # Catalog queries
  catalog(uuid: ID!): OSCALCatalog
  catalogs(
    search: String
    frameworkType: String
    version: String
    limit: Int = 20
    offset: Int = 0
  ): CatalogConnection!
  
  # Control queries
  control(catalogUuid: ID!, controlId: String!): Control
  controls(
    catalogUuid: ID!
    family: String
    implementationStatus: ImplementationStatus
  ): [Control!]!
  
  # System queries
  system(uuid: ID!): SystemSecurityPlan
  systems(
    search: String
    environment: String
    complianceLevel: String
  ): SystemConnection!
  
  # Assessment queries
  assessmentPlan(uuid: ID!): AssessmentPlan
  assessmentResults(uuid: ID!): AssessmentResults
  assessments(
    systemUuid: ID
    dateRange: DateRange
    status: AssessmentStatus
  ): AssessmentConnection!
  
  # Cross-document queries
  controlImplementations(controlId: String!): [ControlImplementation!]!
  findingsForControl(controlId: String!): [Finding!]!
  complianceStatus(systemUuid: ID!): ComplianceStatus!
}
```

#### Mutation Operations
```graphql
type Mutation {
  # Catalog mutations
  createCatalog(input: CreateCatalogInput!): CreateCatalogPayload!
  updateCatalog(uuid: ID!, input: UpdateCatalogInput!): UpdateCatalogPayload!
  deleteCatalog(uuid: ID!): DeleteCatalogPayload!
  
  # System mutations
  createSystem(input: CreateSystemInput!): CreateSystemPayload!
  updateSystemImplementation(
    systemUuid: ID!
    input: UpdateImplementationInput!
  ): UpdateImplementationPayload!
  
  # Assessment mutations
  createAssessmentPlan(input: CreateAssessmentPlanInput!): CreateAssessmentPlanPayload!
  executeAssessment(planUuid: ID!, options: ExecutionOptions): ExecuteAssessmentPayload!
  
  # Validation and conversion
  validateOSCALDocument(input: ValidateDocumentInput!): ValidationResult!
  convertOSCALFormat(
    input: ConvertFormatInput!
  ): ConvertFormatPayload!
}
```

#### Subscription Operations
```graphql
type Subscription {
  # Real-time updates
  oscalDocumentUpdated(uuid: ID!): OSCALDocumentUpdate!
  assessmentProgress(executionId: ID!): AssessmentProgress!
  complianceStatusChanged(systemUuid: ID!): ComplianceStatusUpdate!
  
  # Blockchain events
  blockchainVerification(documentUuid: ID!): BlockchainVerificationEvent!
  
  # Agent activities
  agentActivity(agentType: AgentType): AgentActivityEvent!
}
```

### GraphQL Query Examples

#### Complex Cross-Document Query
```graphql
query SystemComplianceOverview($systemUuid: ID!) {
  system(uuid: $systemUuid) {
    uuid
    metadata {
      title
      version
      lastModified
    }
    controlImplementation {
      implementedRequirements {
        controlId
        implementationStatus {
          state
        }
        statements {
          description
          byComponents {
            componentUuid
            implementationStatus {
              state
            }
          }
        }
      }
    }
    complianceSummary {
      overallScore
      implementedCount
      totalCount
      riskLevel
    }
  }
  
  assessments(systemUuid: $systemUuid, limit: 5) {
    edges {
      node {
        uuid
        metadata {
          title
          lastModified
        }
        summary {
          overallScore
          findingsCount {
            critical
            high
            medium
            low
          }
        }
      }
    }
  }
}
```

## Event-Driven API Patterns

### OSCAL Event Streaming

#### Event Types and Schema
```json
{
  "event_types": {
    "oscal.document.created": {
      "schema": {
        "document_uuid": "string",
        "document_type": "string",
        "created_by": "string",
        "timestamp": "datetime",
        "blockchain_tx_id": "string"
      }
    },
    "oscal.document.updated": {
      "schema": {
        "document_uuid": "string",
        "document_type": "string",
        "updated_by": "string",
        "changes": "object",
        "timestamp": "datetime",
        "blockchain_tx_id": "string"
      }
    },
    "oscal.assessment.completed": {
      "schema": {
        "assessment_uuid": "string",
        "system_uuid": "string",
        "findings_count": "object",
        "overall_score": "number",
        "timestamp": "datetime"
      }
    },
    "oscal.finding.created": {
      "schema": {
        "finding_uuid": "string",
        "control_id": "string",
        "severity": "string",
        "system_uuid": "string",
        "timestamp": "datetime"
      }
    }
  }
}
```

#### WebSocket Event Streaming
```javascript
// Client-side WebSocket connection
const ws = new WebSocket('wss://api.grcos.example.com/events');

ws.onopen = function() {
  // Subscribe to specific event types
  ws.send(JSON.stringify({
    action: 'subscribe',
    event_types: [
      'oscal.document.updated',
      'oscal.assessment.completed',
      'oscal.finding.created'
    ],
    filters: {
      system_uuid: 'ssp-prod-001'
    }
  }));
};

ws.onmessage = function(event) {
  const eventData = JSON.parse(event.data);
  
  switch(eventData.type) {
    case 'oscal.document.updated':
      handleDocumentUpdate(eventData);
      break;
    case 'oscal.assessment.completed':
      handleAssessmentCompletion(eventData);
      break;
    case 'oscal.finding.created':
      handleNewFinding(eventData);
      break;
  }
};
```

### Webhook Integration

#### Webhook Configuration API
```http
# Register webhook
POST /api/v1/webhooks
Content-Type: application/json

{
  "url": "https://external-system.example.com/oscal-webhook",
  "events": [
    "oscal.document.created",
    "oscal.assessment.completed"
  ],
  "filters": {
    "document_type": "system-security-plan",
    "system_environment": "production"
  },
  "secret": "webhook-secret-key",
  "active": true
}

# Response
HTTP/1.1 201 Created
{
  "webhook_id": "wh-001",
  "status": "active",
  "verification_token": "verify-token-123"
}
```

#### Webhook Payload Example
```json
{
  "webhook_id": "wh-001",
  "event_type": "oscal.assessment.completed",
  "timestamp": "2024-01-20T16:30:00Z",
  "data": {
    "assessment_uuid": "ar-q1-2024",
    "system_uuid": "ssp-prod-001",
    "overall_score": 85,
    "findings_count": {
      "critical": 0,
      "high": 2,
      "medium": 5,
      "low": 8
    },
    "risk_level": "medium",
    "assessment_url": "https://api.grcos.example.com/api/v1/oscal/assessments/results/ar-q1-2024"
  },
  "signature": "sha256=abc123def456..."
}
```

## External System Integration Patterns

### Third-Party GRC Tool Integration

#### OSCAL Export API
```http
# Export OSCAL documents for external tools
GET /api/v1/oscal/export/systems/ssp-prod-001?format=json&include=assessments
Accept: application/json

# Response
HTTP/1.1 200 OK
{
  "export_package": {
    "system_security_plan": {...},
    "assessment_results": [...],
    "poam": {...},
    "metadata": {
      "export_timestamp": "2024-01-20T16:30:00Z",
      "export_format": "oscal-json",
      "grcos_version": "2.0.0"
    }
  },
  "download_url": "https://api.grcos.example.com/downloads/export-001.zip",
  "expires_at": "2024-01-21T16:30:00Z"
}
```

### Compliance Reporting Integration

#### Regulatory Report Generation
```http
# Generate FedRAMP compliance report
POST /api/v1/reports/fedramp
Content-Type: application/json

{
  "system_uuid": "ssp-prod-001",
  "assessment_period": {
    "start": "2024-01-01",
    "end": "2024-03-31"
  },
  "report_type": "ato_package",
  "include_evidence": true
}

# Response
HTTP/1.1 202 Accepted
{
  "report_id": "rpt-fedramp-001",
  "status": "generating",
  "estimated_completion": "2024-01-20T17:00:00Z",
  "progress_url": "/api/v1/reports/rpt-fedramp-001/progress"
}
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS API Team
