---
name: Feature request
about: Let us know how we could improve OPA
title: ''
labels: feature-request
assignees: ''

---

<!-- Thanks for opening an issue to request a feature or file a bug!
If you provide some basic information, it helps us address problems faster. -->

## What is the underlying problem you're trying to solve?
<!--
By making this change, what are you hoping to improve or fix?
Why would this change make the OPA experience better?
Are there any current solutions that are inefficient or frustrating?
-->

## Describe the ideal solution
<!--
In the ideal scenario, there are more than enough resources to solve any problem. Describe what this solution would look like if the resources were available.
-->

## Describe a "Good Enough" solution
<!--
In a more realistic world, we have limited time and resources to solve a problem. Describe what a minimum viable solution would look like that still satisfies the requirements. Think about what is a must-have and what is a nice-to-have; now list out the must-haves. Is there an alternate solution that would work just as well?
-->

## Additional Context
<!--
Add in additional information that would help. Do you have links to similar solutions, screenshots of a problem, or mockups of a solution? 
-->
