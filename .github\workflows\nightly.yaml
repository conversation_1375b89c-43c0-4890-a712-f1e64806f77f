name: Nightly
on:
  workflow_dispatch: {}   # Allow for manual triggers
  schedule:
    - cron:  '0 8 * * 0-4' # Sun-Thu, at 8:00 UTC

permissions: 
  contents: read
  
jobs:
  race-detector:
    name: Go Race Detector
    runs-on: ubuntu-24.04
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Test with Race Detector
        run: CGO_ENABLED=1 make ci-go-race-detector

      - name: Slack Notification
        uses: 8398a7/action-slack@1750b5085f3ec60384090fb7c52965ef822e869e # v3.18.0
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_NOTIFICATION_WEBHOOK }}
        if: ${{ failure() && env.SLACK_WEBHOOK_URL }}
        with:
          status: ${{ job.status }}
          fields: repo,workflow

  native-fuzzer:
    name: <PERSON> Fuzzer (native)
    runs-on: ubuntu-24.04
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - id: go_version
        name: Read go version
        run: echo "go_version=$(cat .go-version)" >> $GITHUB_OUTPUT

      - name: Install Go (${{ steps.go_version.outputs.go_version }})
        uses: actions/setup-go@0aaccfd150d50ccaeb58ebd88d36e91967a5f35b # v5.4.0
        with:
          go-version: ${{ steps.go_version.outputs.go_version }}

      - name: go test -fuzz
        run: go test ./ast -fuzz FuzzParseStatementsAndCompileModules -fuzztime 1h -v -run '^$'

      - name: Dump crashers
        if: ${{ failure() }}
        run: find ast/testdata/fuzz ! -name '*.stmt' ! -type d -print -exec cat {} \;

      - name: Slack Notification
        uses: 8398a7/action-slack@1750b5085f3ec60384090fb7c52965ef822e869e # v3.18.0
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_NOTIFICATION_WEBHOOK }}
        if: ${{ failure() && env.SLACK_WEBHOOK_URL }}
        with:
          status: ${{ job.status }}
          fields: repo,workflow

  go-perf:
    name: Go Perf
    runs-on: ubuntu-24.04
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Benchmark Test Golang
        run: make ci-go-perf
        timeout-minutes: 45
        env:
          DOCKER_RUNNING: 0

      - name: Slack Notification
        uses: 8398a7/action-slack@1750b5085f3ec60384090fb7c52965ef822e869e # v3.18.0
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_NOTIFICATION_WEBHOOK }}
        if: ${{ failure() && env.SLACK_WEBHOOK_URL }}
        with:
          status: ${{ job.status }}
          fields: repo,workflow

  trivy-scan-image:
    name: Trivy security scan image
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code # needed for .trivyignore file
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - run: "docker pull openpolicyagent/opa:edge-static"

      # Equivalent to:
      # $ trivy image openpolicyagent/opa:edge-static
      - name: Run Trivy scan on image
        uses: aquasecurity/trivy-action@6c175e9c4083a92bbca2f9724c8a5e33bc2d97a5 # 0.30.0
        with:
          image-ref: 'openpolicyagent/opa:edge-static'
          format: table
          exit-code: '1'
          ignore-unfixed: true
          vuln-type: os,library
          severity: CRITICAL,HIGH
        env:
          TRIVY_DB_REPOSITORY: ghcr.io/aquasecurity/trivy-db,public.ecr.aws/aquasecurity/trivy-db

      - name: Slack Notification
        uses: 8398a7/action-slack@1750b5085f3ec60384090fb7c52965ef822e869e # v3.18.0
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_NOTIFICATION_WEBHOOK }}
        if: ${{ failure() && env.SLACK_WEBHOOK_URL }}
        with:
          status: ${{ job.status }}
          fields: repo,workflow

  trivy-scan-repo:
    name: Trivy security scan repo
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      # Equivalent to:
      # $ trivy fs .
      - name: Run Trivy scan on repo
        uses: aquasecurity/trivy-action@6c175e9c4083a92bbca2f9724c8a5e33bc2d97a5 # 0.30.0
        with:
          scan-type: fs
          format: table
          exit-code: '1'
          ignore-unfixed: true
          skip-dirs: vendor/
          severity: CRITICAL,HIGH
        env:
          TRIVY_DB_REPOSITORY: ghcr.io/aquasecurity/trivy-db,public.ecr.aws/aquasecurity/trivy-db

      - name: Slack Notification
        uses: 8398a7/action-slack@1750b5085f3ec60384090fb7c52965ef822e869e # v3.18.0
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_NOTIFICATION_WEBHOOK }}
        if: ${{ failure() && env.SLACK_WEBHOOK_URL }}
        with:
          status: ${{ job.status }}
          fields: repo,workflow

  govulncheck:
    name: Go vulnerability check
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - id: go_version
        name: Read go version
        run: echo "go_version=$(cat .go-version)" >> $GITHUB_OUTPUT

      - name: Install Go (${{ steps.go_version.outputs.go_version }})
        uses: actions/setup-go@0aaccfd150d50ccaeb58ebd88d36e91967a5f35b # v5.4.0
        with:
          go-version: ${{ steps.go_version.outputs.go_version }}

      - run: go install golang.org/x/vuln/cmd/govulncheck@latest
      - run: govulncheck ./...

      - name: Slack Notification
        uses: 8398a7/action-slack@1750b5085f3ec60384090fb7c52965ef822e869e # v3.18.0
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_NOTIFICATION_WEBHOOK }}
        if: ${{ failure() && env.SLACK_WEBHOOK_URL }}
        with:
          status: ${{ job.status }}
          fields: repo,workflow
