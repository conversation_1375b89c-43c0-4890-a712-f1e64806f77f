# GRCOS OSCAL Monitoring and Maintenance

## Overview

This document defines comprehensive monitoring and maintenance procedures for OSCAL integration within the GRCOS platform. It covers performance monitoring, health checks, automated maintenance tasks, capacity planning, and operational procedures for ensuring optimal OSCAL system performance and reliability.

## Monitoring Architecture

### Multi-Layer Monitoring Strategy

#### Monitoring Stack Components
```
Application Layer Monitoring:
├── OSCAL Service Metrics (Response times, throughput, errors)
├── AI Agent Performance (Task completion, processing times)
├── Blockchain Transaction Monitoring (Block times, consensus health)
└── Database Performance (Query times, connection pools)

Infrastructure Layer Monitoring:
├── Kubernetes Cluster Health (Node status, resource utilization)
├── Container Metrics (CPU, memory, disk, network)
├── Storage Performance (IOPS, latency, capacity)
└── Network Performance (Bandwidth, latency, packet loss)

Business Logic Monitoring:
├── Compliance Score Trends (System compliance over time)
├── Assessment Completion Rates (Success/failure rates)
├── Finding Resolution Times (Time to remediate findings)
└── Document Processing Metrics (OSCAL document lifecycle)
```

#### Prometheus Metrics Configuration
```yaml
# prometheus-config.yaml
global:
  scrape_interval: 30s
  evaluation_interval: 30s

rule_files:
  - "grcos-oscal-rules.yml"

scrape_configs:
  - job_name: 'grcos-oscal-service'
    static_configs:
      - targets: ['grcos-oscal-service:8080']
    metrics_path: '/metrics'
    scrape_interval: 15s
    
  - job_name: 'grcos-ai-agents'
    static_configs:
      - targets: ['grcos-ai-agents:8081']
    metrics_path: '/metrics'
    scrape_interval: 30s
    
  - job_name: 'blockchain-peers'
    static_configs:
      - targets: 
        - 'peer0-grcos-org:9443'
        - 'peer1-grcos-org:9443'
        - 'peer2-grcos-org:9443'
    metrics_path: '/metrics'
    scrape_interval: 60s
    
  - job_name: 'mongodb-exporter'
    static_configs:
      - targets: ['mongodb-exporter:9216']
    scrape_interval: 30s
    
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### Custom OSCAL Metrics

#### Application-Specific Metrics
```python
from prometheus_client import Counter, Histogram, Gauge, Summary
import time

class OSCALMetricsCollector:
    """
    Collect OSCAL-specific metrics for monitoring
    """
    
    def __init__(self):
        # Document processing metrics
        self.documents_processed = Counter(
            'oscal_documents_processed_total',
            'Total number of OSCAL documents processed',
            ['document_type', 'operation', 'status']
        )
        
        self.document_processing_time = Histogram(
            'oscal_document_processing_seconds',
            'Time spent processing OSCAL documents',
            ['document_type', 'operation'],
            buckets=[0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0]
        )
        
        # Validation metrics
        self.validation_results = Counter(
            'oscal_validation_results_total',
            'OSCAL document validation results',
            ['document_type', 'validation_type', 'result']
        )
        
        # Blockchain metrics
        self.blockchain_transactions = Counter(
            'oscal_blockchain_transactions_total',
            'OSCAL blockchain transactions',
            ['transaction_type', 'status']
        )
        
        self.blockchain_verification_time = Histogram(
            'oscal_blockchain_verification_seconds',
            'Time spent verifying OSCAL documents on blockchain',
            buckets=[0.5, 1.0, 2.0, 5.0, 10.0, 30.0]
        )
        
        # Compliance metrics
        self.compliance_scores = Gauge(
            'oscal_system_compliance_score',
            'Current compliance score for systems',
            ['system_uuid', 'system_name']
        )
        
        self.active_findings = Gauge(
            'oscal_active_findings',
            'Number of active findings by severity',
            ['system_uuid', 'severity']
        )
        
        # Agent metrics
        self.agent_task_duration = Histogram(
            'oscal_agent_task_duration_seconds',
            'Duration of agent tasks',
            ['agent_type', 'task_type'],
            buckets=[1.0, 5.0, 10.0, 30.0, 60.0, 300.0, 600.0]
        )
        
        self.agent_task_queue_size = Gauge(
            'oscal_agent_task_queue_size',
            'Number of tasks in agent queues',
            ['agent_type']
        )
    
    def record_document_processing(self, document_type, operation, status, processing_time):
        """Record document processing metrics"""
        self.documents_processed.labels(
            document_type=document_type,
            operation=operation,
            status=status
        ).inc()
        
        self.document_processing_time.labels(
            document_type=document_type,
            operation=operation
        ).observe(processing_time)
    
    def record_validation_result(self, document_type, validation_type, result):
        """Record validation metrics"""
        self.validation_results.labels(
            document_type=document_type,
            validation_type=validation_type,
            result=result
        ).inc()
    
    def record_blockchain_transaction(self, transaction_type, status, verification_time=None):
        """Record blockchain transaction metrics"""
        self.blockchain_transactions.labels(
            transaction_type=transaction_type,
            status=status
        ).inc()
        
        if verification_time is not None:
            self.blockchain_verification_time.observe(verification_time)
    
    def update_compliance_score(self, system_uuid, system_name, score):
        """Update compliance score gauge"""
        self.compliance_scores.labels(
            system_uuid=system_uuid,
            system_name=system_name
        ).set(score)
    
    def update_active_findings(self, system_uuid, severity, count):
        """Update active findings gauge"""
        self.active_findings.labels(
            system_uuid=system_uuid,
            severity=severity
        ).set(count)
```

## Health Check Framework

### Comprehensive Health Monitoring

#### Health Check Service
```python
class OSCALHealthChecker:
    """
    Comprehensive health checking for OSCAL services
    """
    
    def __init__(self, oscal_repository, blockchain_client, redis_client):
        self.oscal_repository = oscal_repository
        self.blockchain_client = blockchain_client
        self.redis_client = redis_client
        self.health_checks = self._initialize_health_checks()
    
    def perform_health_check(self, check_type="full"):
        """
        Perform comprehensive health check
        """
        health_result = {
            "timestamp": datetime.utcnow(),
            "check_type": check_type,
            "overall_status": "healthy",
            "checks": {},
            "warnings": [],
            "errors": []
        }
        
        # Basic health checks (always performed)
        basic_checks = [
            "database_connectivity",
            "cache_connectivity", 
            "api_responsiveness"
        ]
        
        # Extended health checks (for full check)
        extended_checks = [
            "blockchain_connectivity",
            "agent_health",
            "document_validation",
            "performance_metrics"
        ]
        
        checks_to_perform = basic_checks
        if check_type == "full":
            checks_to_perform.extend(extended_checks)
        
        # Perform health checks
        for check_name in checks_to_perform:
            try:
                check_result = self._perform_individual_check(check_name)
                health_result["checks"][check_name] = check_result
                
                if check_result["status"] == "unhealthy":
                    health_result["overall_status"] = "unhealthy"
                    health_result["errors"].append(check_result["message"])
                elif check_result["status"] == "degraded":
                    if health_result["overall_status"] == "healthy":
                        health_result["overall_status"] = "degraded"
                    health_result["warnings"].append(check_result["message"])
                    
            except Exception as e:
                health_result["checks"][check_name] = {
                    "status": "unhealthy",
                    "message": f"Health check failed: {str(e)}",
                    "timestamp": datetime.utcnow()
                }
                health_result["overall_status"] = "unhealthy"
                health_result["errors"].append(f"{check_name}: {str(e)}")
        
        return health_result
    
    def _perform_individual_check(self, check_name):
        """
        Perform individual health check
        """
        check_start = time.time()
        
        if check_name == "database_connectivity":
            result = self._check_database_health()
        elif check_name == "cache_connectivity":
            result = self._check_cache_health()
        elif check_name == "blockchain_connectivity":
            result = self._check_blockchain_health()
        elif check_name == "agent_health":
            result = self._check_agent_health()
        elif check_name == "api_responsiveness":
            result = self._check_api_responsiveness()
        elif check_name == "document_validation":
            result = self._check_document_validation()
        elif check_name == "performance_metrics":
            result = self._check_performance_metrics()
        else:
            result = {
                "status": "unhealthy",
                "message": f"Unknown health check: {check_name}"
            }
        
        result["duration"] = time.time() - check_start
        result["timestamp"] = datetime.utcnow()
        
        return result
    
    def _check_database_health(self):
        """Check MongoDB database health"""
        try:
            # Test basic connectivity
            ping_result = self.oscal_repository.ping()
            if not ping_result:
                return {
                    "status": "unhealthy",
                    "message": "Database ping failed"
                }
            
            # Test query performance
            query_start = time.time()
            test_query_result = self.oscal_repository.count_documents("oscal_catalogs")
            query_time = time.time() - query_start
            
            if query_time > 5.0:  # 5 second threshold
                return {
                    "status": "degraded",
                    "message": f"Database queries slow: {query_time:.2f}s",
                    "query_time": query_time
                }
            
            # Check connection pool
            pool_stats = self.oscal_repository.get_connection_pool_stats()
            if pool_stats["available_connections"] < 2:
                return {
                    "status": "degraded",
                    "message": "Low database connection pool availability",
                    "pool_stats": pool_stats
                }
            
            return {
                "status": "healthy",
                "message": "Database connectivity normal",
                "query_time": query_time,
                "pool_stats": pool_stats
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "message": f"Database health check failed: {str(e)}"
            }
    
    def _check_blockchain_health(self):
        """Check blockchain network health"""
        try:
            # Test peer connectivity
            peer_status = self.blockchain_client.check_peer_status()
            healthy_peers = sum(1 for peer in peer_status if peer["status"] == "healthy")
            total_peers = len(peer_status)
            
            if healthy_peers == 0:
                return {
                    "status": "unhealthy",
                    "message": "No healthy blockchain peers available",
                    "peer_status": peer_status
                }
            elif healthy_peers < total_peers * 0.67:  # Less than 2/3 healthy
                return {
                    "status": "degraded",
                    "message": f"Only {healthy_peers}/{total_peers} blockchain peers healthy",
                    "peer_status": peer_status
                }
            
            # Test transaction capability
            test_tx_start = time.time()
            test_result = self.blockchain_client.test_transaction()
            tx_time = time.time() - test_tx_start
            
            if not test_result["success"]:
                return {
                    "status": "unhealthy",
                    "message": f"Blockchain transaction test failed: {test_result['error']}",
                    "peer_status": peer_status
                }
            
            if tx_time > 30.0:  # 30 second threshold
                return {
                    "status": "degraded",
                    "message": f"Blockchain transactions slow: {tx_time:.2f}s",
                    "transaction_time": tx_time,
                    "peer_status": peer_status
                }
            
            return {
                "status": "healthy",
                "message": "Blockchain network healthy",
                "healthy_peers": f"{healthy_peers}/{total_peers}",
                "transaction_time": tx_time
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "message": f"Blockchain health check failed: {str(e)}"
            }
```

## Automated Maintenance Tasks

### Maintenance Automation Framework

#### Scheduled Maintenance Tasks
```python
class OSCALMaintenanceScheduler:
    """
    Automated maintenance tasks for OSCAL system
    """
    
    def __init__(self, oscal_repository, blockchain_client, metrics_collector):
        self.oscal_repository = oscal_repository
        self.blockchain_client = blockchain_client
        self.metrics_collector = metrics_collector
        self.maintenance_tasks = self._initialize_maintenance_tasks()
    
    def schedule_maintenance_tasks(self):
        """
        Schedule all automated maintenance tasks
        """
        maintenance_schedule = {
            # Daily tasks
            "daily": [
                {"task": "cleanup_expired_cache", "time": "02:00"},
                {"task": "verify_document_integrity", "time": "03:00"},
                {"task": "update_compliance_metrics", "time": "04:00"},
                {"task": "cleanup_old_logs", "time": "05:00"}
            ],
            
            # Weekly tasks  
            "weekly": [
                {"task": "database_optimization", "day": "sunday", "time": "01:00"},
                {"task": "blockchain_health_report", "day": "sunday", "time": "02:00"},
                {"task": "performance_analysis", "day": "sunday", "time": "03:00"}
            ],
            
            # Monthly tasks
            "monthly": [
                {"task": "archive_old_assessments", "day": 1, "time": "00:00"},
                {"task": "capacity_planning_analysis", "day": 1, "time": "01:00"},
                {"task": "security_audit", "day": 15, "time": "02:00"}
            ]
        }
        
        # Register tasks with scheduler
        for frequency, tasks in maintenance_schedule.items():
            for task_config in tasks:
                self._register_maintenance_task(frequency, task_config)
    
    def execute_maintenance_task(self, task_name, task_params=None):
        """
        Execute specific maintenance task
        """
        task_start = time.time()
        task_result = {
            "task_name": task_name,
            "start_time": datetime.utcnow(),
            "status": "running",
            "details": {}
        }
        
        try:
            if task_name == "cleanup_expired_cache":
                result = self._cleanup_expired_cache()
            elif task_name == "verify_document_integrity":
                result = self._verify_document_integrity()
            elif task_name == "update_compliance_metrics":
                result = self._update_compliance_metrics()
            elif task_name == "database_optimization":
                result = self._optimize_database()
            elif task_name == "archive_old_assessments":
                result = self._archive_old_assessments()
            elif task_name == "capacity_planning_analysis":
                result = self._perform_capacity_analysis()
            else:
                raise ValueError(f"Unknown maintenance task: {task_name}")
            
            task_result["status"] = "completed"
            task_result["details"] = result
            
        except Exception as e:
            task_result["status"] = "failed"
            task_result["error"] = str(e)
        
        task_result["end_time"] = datetime.utcnow()
        task_result["duration"] = time.time() - task_start
        
        # Log maintenance task result
        self._log_maintenance_result(task_result)
        
        return task_result
    
    def _verify_document_integrity(self):
        """
        Verify integrity of OSCAL documents against blockchain
        """
        verification_result = {
            "documents_checked": 0,
            "documents_verified": 0,
            "integrity_failures": [],
            "blockchain_mismatches": []
        }
        
        # Get all active documents
        active_documents = self.oscal_repository.get_active_documents()
        
        for document in active_documents:
            verification_result["documents_checked"] += 1
            
            try:
                # Calculate current document hash
                current_hash = self._calculate_document_hash(document)
                
                # Get blockchain record
                blockchain_record = self.blockchain_client.get_document_record(document["uuid"])
                
                if not blockchain_record:
                    verification_result["integrity_failures"].append({
                        "document_uuid": document["uuid"],
                        "error": "No blockchain record found"
                    })
                    continue
                
                stored_hash = blockchain_record.get("content_hash")
                
                if current_hash != stored_hash:
                    verification_result["blockchain_mismatches"].append({
                        "document_uuid": document["uuid"],
                        "current_hash": current_hash,
                        "blockchain_hash": stored_hash
                    })
                else:
                    verification_result["documents_verified"] += 1
                    
            except Exception as e:
                verification_result["integrity_failures"].append({
                    "document_uuid": document["uuid"],
                    "error": str(e)
                })
        
        return verification_result
    
    def _optimize_database(self):
        """
        Perform database optimization tasks
        """
        optimization_result = {
            "indexes_analyzed": 0,
            "indexes_optimized": 0,
            "collections_compacted": 0,
            "space_reclaimed_mb": 0
        }
        
        # Analyze and optimize indexes
        collections = self.oscal_repository.get_collection_names()
        
        for collection_name in collections:
            try:
                # Analyze index usage
                index_stats = self.oscal_repository.get_index_stats(collection_name)
                optimization_result["indexes_analyzed"] += len(index_stats)
                
                # Identify unused indexes
                unused_indexes = [
                    idx for idx in index_stats 
                    if idx["usage_count"] == 0 and idx["name"] != "_id_"
                ]
                
                # Drop unused indexes
                for unused_idx in unused_indexes:
                    self.oscal_repository.drop_index(collection_name, unused_idx["name"])
                    optimization_result["indexes_optimized"] += 1
                
                # Compact collection if fragmented
                fragmentation = self.oscal_repository.get_collection_fragmentation(collection_name)
                if fragmentation > 0.3:  # 30% fragmentation threshold
                    compact_result = self.oscal_repository.compact_collection(collection_name)
                    optimization_result["collections_compacted"] += 1
                    optimization_result["space_reclaimed_mb"] += compact_result.get("space_reclaimed", 0)
                
            except Exception as e:
                # Log optimization error but continue with other collections
                self._log_optimization_error(collection_name, str(e))
        
        return optimization_result
```

## Performance Monitoring and Alerting

### Alert Rules Configuration

#### Prometheus Alert Rules
```yaml
# grcos-oscal-rules.yml
groups:
  - name: grcos-oscal-alerts
    rules:
      # High-level service alerts
      - alert: OSCALServiceDown
        expr: up{job="grcos-oscal-service"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "GRCOS OSCAL Service is down"
          description: "GRCOS OSCAL Service has been down for more than 1 minute"
          
      - alert: OSCALServiceHighErrorRate
        expr: rate(oscal_documents_processed_total{status="error"}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate in OSCAL document processing"
          description: "Error rate is {{ $value }} errors per second"
          
      # Performance alerts
      - alert: OSCALDocumentProcessingSlow
        expr: histogram_quantile(0.95, rate(oscal_document_processing_seconds_bucket[5m])) > 30
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "OSCAL document processing is slow"
          description: "95th percentile processing time is {{ $value }} seconds"
          
      - alert: BlockchainVerificationSlow
        expr: histogram_quantile(0.95, rate(oscal_blockchain_verification_seconds_bucket[5m])) > 60
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Blockchain verification is slow"
          description: "95th percentile verification time is {{ $value }} seconds"
          
      # Compliance alerts
      - alert: ComplianceScoreDropped
        expr: decrease(oscal_system_compliance_score[1h]) > 10
        for: 0m
        labels:
          severity: warning
        annotations:
          summary: "System compliance score dropped significantly"
          description: "Compliance score for {{ $labels.system_name }} dropped by {{ $value }} points"
          
      - alert: CriticalFindingsDetected
        expr: increase(oscal_active_findings{severity="critical"}[5m]) > 0
        for: 0m
        labels:
          severity: critical
        annotations:
          summary: "Critical security findings detected"
          description: "{{ $value }} new critical findings detected in {{ $labels.system_uuid }}"
          
      # Infrastructure alerts
      - alert: DatabaseConnectionPoolLow
        expr: mongodb_connections_available < 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "MongoDB connection pool running low"
          description: "Only {{ $value }} database connections available"
          
      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis memory usage high"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Operations Team
