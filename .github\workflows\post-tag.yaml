name: Post Tag

on:
  push:
    tags:
      - "*"

jobs:
  generate:
    name: Generate Code
    runs-on: ubuntu-24.04
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          token: ${{ secrets.GH_PUSH_TOKEN }}

      - name: Generate
        run: make clean generate

  release-build:
    name: Release Build (linux, windows)
    runs-on: ubuntu-24.04
    needs: generate
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: Git Describe
        run: git describe --tags

      - name: Build Linux and Windows
        run: make ci-go-ci-build-linux ci-go-ci-build-linux-static ci-go-ci-build-windows
        timeout-minutes: 30
        env:
          TELEMETRY_URL: ${{ secrets.TELEMETRY_URL }}

      - name: Build Linux arm64
        run: make ci-go-ci-build-linux-static
        timeout-minutes: 30
        env:
          GOARCH: arm64
          TELEMETRY_URL: ${{ secrets.TELEMETRY_URL }}

      - name: Upload binaries
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        if: always()
        with:
          name: binaries-linux-windows
          path: _release

  release-build-darwin:
    name: Release Build (darwin)
    runs-on: macos-14
    needs: generate
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: Git Describe
        run: git describe --tags

      - id: go_version
        name: Read go version
        run: echo "go_version=$(cat .go-version)" >> $GITHUB_OUTPUT

      - name: Install Go (${{ steps.go_version.outputs.go_version }})
        uses: actions/setup-go@0aaccfd150d50ccaeb58ebd88d36e91967a5f35b # v5.4.0
        with:
          go-version: ${{ steps.go_version.outputs.go_version }}

      - name: Build Darwin
        run: |
          make ci-build-darwin GOARCH=amd64
          make ci-build-darwin-arm64-static
        timeout-minutes: 30
        env:
          TELEMETRY_URL: ${{ secrets.TELEMETRY_URL }}

      - name: Upload binaries (darwin)
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        if: always()
        with:
          name: binaries-darwin
          path: _release

  build:
    name: Push Latest Release
    needs: [release-build, release-build-darwin]
    runs-on: ubuntu-24.04
    steps:
      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set TAG_NAME in Environment
        # Subsequent jobs will be have the computed tag name
        run: echo "TAG_NAME=${GITHUB_REF##*/}" >> $GITHUB_ENV

      - name: Download release binaries
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          pattern: binaries-*
          merge-multiple: true
          path: _release

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@b5ca514318bd6ebac0fb2aedd5d36ec1b5c232a2 # v3.10.0

      - name: Build and Deploy OPA Docker Images
        id: build-and-deploy
        env:
          DOCKER_USER: ${{ secrets.DOCKER_USER }}
          DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
          DOCKER_IMAGE: ${{ secrets.DOCKER_IMAGE }}
        # Only run if required secrets are provided
        if: ${{ env.DOCKER_USER && env.DOCKER_PASSWORD }}
        run: make release-ci

      - name: Create or Update Release
        env:
          # Required for the GitHub CLI
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: ./build/github-release.sh --asset-dir=$(make release-dir) --tag=${TAG_NAME}
